'use client';

import React, { useCallback, useMemo, useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Loader2, RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/app/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { HierarchicalPartsForm } from '@/app/components/forms/HierarchicalPartsForm';
import { useAssemblyForm } from '@/app/contexts/AssemblyFormContext';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { mergePartsRequiredData, validatePartDataStructure } from '@/app/utils/partDataPreservation';

// SCHEMA ALIGNMENT: Updated all mapping and form logic to use canonical assembly schema from database_schema_updated.md. Legacy/incorrect fields removed or migrated.
// ... existing code ...
// In all mapping logic, use only:
// - partsRequired: [{ partId, quantityRequired, unitOfMeasure }]
// Remove mapping for parts, quantity, part_id.
// ... existing code ...

// Helper to normalize parts for a more stable comparison (moved outside and wrapped in useCallback)
const normalizePartsForComparison = (partsArray: any[]) => {
  if (!Array.isArray(partsArray)) return [];
  return partsArray.map((part, index) => ({
    id: part?._id || part?.id || part?.clientId || `temp-part-${index}`,
    partId: (typeof part?.partId === 'object' ? part?.partId?._id : part?.partId) || '',
    quantityRequired: part?.quantityRequired || 0,
    unitOfMeasure: part?.unitOfMeasure || ''
  })).sort((a, b) => (a.id || '').localeCompare(b.id || ''));
};

/**
 * Assembly form content component - shared between create and edit pages
 */
export default function AssemblyFormContent() {
  const router = useRouter();
  const hierarchicalPartsFormRef = useRef<{ triggerSubmit: () => Promise<void> }>(null);
  const {
    formData,
    isLoading,
    isSaving,
    isEditing,
    isDirty,
    saveAssembly,
    saveAssemblyWithFreshData,
    resetForm,
    setFormData,
    refreshStockData,
    checkStockStaleness,
    getStockRefreshRecommendation,
    autoRefreshStockIfNeeded
  } = useAssemblyForm();

  // State for stock staleness monitoring
  const [stockStaleness, setStockStaleness] = useState<{
    hasStaleData: boolean;
    staleParts: string[];
    recommendations: string[];
  }>({ hasStaleData: false, staleParts: [], recommendations: [] });

  const [refreshRecommendation, setRefreshRecommendation] = useState<{
    shouldRefresh: boolean;
    reasons: string[];
    priority: 'low' | 'medium' | 'high';
  }>({ shouldRefresh: false, reasons: [], priority: 'low' });

  // Monitor stock staleness when formData changes
  useEffect(() => {
    if (formData?.partsRequired) {
      const staleness = checkStockStaleness();
      const recommendation = getStockRefreshRecommendation();

      setStockStaleness(staleness);
      setRefreshRecommendation(recommendation);

      // Show automatic warnings for high priority issues
      if (recommendation.shouldRefresh && recommendation.priority === 'high') {
        toast.warning('Stock data may be outdated. Consider refreshing before making changes.', {
          action: {
            label: 'Refresh Now',
            onClick: () => handleStockRefresh()
          }
        });
      }
    }
  }, [formData?.partsRequired, checkStockStaleness, getStockRefreshRecommendation]);

  // Enhanced stock refresh handler
  const handleStockRefresh = useCallback(async () => {
    try {
      await refreshStockData();
      toast.success('Stock data refreshed successfully');

      // Update staleness indicators
      const newStaleness = checkStockStaleness();
      const newRecommendation = getStockRefreshRecommendation();
      setStockStaleness(newStaleness);
      setRefreshRecommendation(newRecommendation);
    } catch (error) {
      console.error('Error refreshing stock:', error);
      toast.error('Failed to refresh stock data');
    }
  }, [refreshStockData, checkStockStaleness, getStockRefreshRecommendation]);

  // Memoize formatted parts separately to stabilize its reference
  const memoizedFormattedParts = useMemo(() => {
    if (!formData || !formData.partsRequired) return []; // Ensure formData and partsRequired exist

    // Use formData.partsRequired and extract stock from populated Part objects
    const partsArray = formData.partsRequired;

    return Array.isArray(partsArray)
      ? partsArray.map((part: any, index: number) => {
          const partRef = part?.partId; // partId might be an object or string from DB

          // Use the same direct access pattern as BomViewer for consistency
          const stockValue = typeof partRef === 'object' && partRef !== null && !(partRef instanceof String)
            ? (partRef.inventory?.currentStock || 0)
            : 0;

          return {
            ...part, // Spread the original part object first

            // Then, define or override specific fields to ensure correct values and defaults
            id: part?.id || part?._id || part?.clientId || `temp-part-${index}`,
            partId: (typeof partRef === 'object' && partRef !== null && !(partRef instanceof String) ? partRef._id : partRef) || part?.partId || '',
            name: part?.name || (typeof partRef === 'object' ? partRef?.name : null) || 'Unknown Part',
            description: part?.description || (typeof partRef === 'object' ? partRef?.description : null) || '',
            quantityRequired: part?.quantityRequired || 1,
            unitOfMeasure: part?.unitOfMeasure || (typeof partRef === 'object' ? partRef?.unitOfMeasure : null) || 'PCS',
            cost: part?.cost || (typeof partRef === 'object' ? partRef?.cost : null) || 0,
            notes: part?.notes || '',
            currentStock: stockValue,
            isAssembly: part?.isAssembly || (typeof partRef === 'object' ? partRef?.isAssembly : false) || false,
            category: part?.category,
            reorderLevel: part?.reorderLevel,
            supplier: part?.supplier,
            technicalSpecs: part?.technicalSpecs,
            partDisplayIdentifier: part?.partDisplayIdentifier || (typeof partRef === 'object' && partRef !== null && !(partRef instanceof String) ? partRef.partNumber : partRef) || part?.name || '',
            isExpanded: part?.isExpanded === undefined ? true : part.isExpanded,
            children: part?.children || [],

            partId_name: part?.name || (typeof partRef === 'object' ? partRef?.name : null) || 'Unknown Part',
            originalIndex: index,
          };
        })
      : [];
  }, [formData?.partsRequired]); // Only depend on formData.partsRequired

  // Format assembly data for the form - memoized to prevent unnecessary recalculations
  const formattedAssemblyData = useMemo(() => {
    if (!formData) return undefined;

    const formDataAny = formData as any;
    
    // Ensure status is one of the allowed values
    let status = formData?.status || 'pending_review';
    if (status === 'in_production') {
      status = 'active'; // Map legacy 'in_production' to 'active'
    }

    return {
      name: formData?.name || 'Unnamed Assembly',
      description: formData?.description || '',
      assemblyCode: formData?.assemblyCode || formDataAny?.assembly_id || '',
      status: status,
      version: formData?.version || 1,
      isTopLevel: formData?.isTopLevel !== undefined ? formData.isTopLevel : true,
      manufacturingInstructions: formData?.manufacturingInstructions || '',
      estimatedBuildTime: formData?.estimatedBuildTime || '',
      assembly_stage: formDataAny?.assembly_stage || 'Final Assembly',
      partsRequired: memoizedFormattedParts // Use the memoized parts
    };
  }, [
    formData?.name,
    formData?.description,
    formData?.assemblyCode,
    (formData as any)?.assembly_id,
    formData?.status,
    formData?.version,
    formData?.isTopLevel,
    formData?.manufacturingInstructions,
    formData?.estimatedBuildTime,
    (formData as any)?.assembly_stage,
    memoizedFormattedParts // Dependency: the memoized parts array reference
  ]);

  // Handle save button click - now uses ref-based submission
  const handleSave = async () => {
    if (!hierarchicalPartsFormRef.current) {
      console.warn("HierarchicalPartsForm ref not available, cannot save.");
      toast.error("Cannot save assembly. Form component is missing or not ready.");
      return;
    }

    // Check for stock staleness before saving
    if (refreshRecommendation.shouldRefresh && refreshRecommendation.priority === 'medium') {
      const shouldProceed = confirm(
        `Stock data may be outdated for some parts:\n${refreshRecommendation.reasons.join('\n')}\n\nDo you want to refresh stock data before saving?`
      );

      if (shouldProceed) {
        await handleStockRefresh();
      }
    }

    try {
      console.log('[AssemblyFormContent] Starting save process via ref-based submission...');

      // Trigger the child form submission which will call handleFormSubmit with fresh data
      await hierarchicalPartsFormRef.current.triggerSubmit();

      console.log('[AssemblyFormContent] Save process completed successfully');
    } catch (error) {
      console.error('[AssemblyFormContent] Error during save process:', error);
      toast.error('Failed to save assembly. Please try again.');
    }
  };

  // Enhanced part data validation before form submission
  const validatePartsData = useCallback((parts: any[]) => {
    const validation = validatePartDataStructure(parts);
    if (!validation.isValid) {
      console.warn('[AssemblyFormContent] Part data validation issues:', validation.issues);
      // Could show toast warnings here if needed
    }
    return validation.isValid;
  }, []);

  // Handle form submission - now saves directly with fresh data
  const handleFormSubmit = useCallback(async (hierarchicalFormData: any) => {
    console.log('[AssemblyFormContent] handleFormSubmit called with fresh data:', {
      name: hierarchicalFormData.name,
      assemblyCode: hierarchicalFormData.assemblyCode,
      description: hierarchicalFormData.description,
      status: hierarchicalFormData.status,
      partsCount: hierarchicalFormData.partsRequired?.length || 0
    });

    const currentFormData = formData || {};

    // FIXED: Use smart merge instead of direct overwrite to preserve populated Part objects
    const mergedPartsRequired = mergePartsRequiredData(
      currentFormData.partsRequired || [],
      hierarchicalFormData.partsRequired || [],
      {
        preservePopulatedData: true,
        updateStockFromHierarchical: false, // Keep existing stock data unless explicitly refreshed
        logMergeProcess: false // Disable verbose logging for production
      }
    );

    // Validate the merged parts data
    validatePartsData(mergedPartsRequired);

    // Prepare the fresh data for saving
    const freshDataToSave = {
      ...currentFormData, // Start with current context data (preserves _id, etc.)
      name: hierarchicalFormData.name,
      assemblyCode: hierarchicalFormData.assemblyCode,
      description: hierarchicalFormData.description,
      status: hierarchicalFormData.status,
      version: hierarchicalFormData.version,
      isTopLevel: hierarchicalFormData.isTopLevel,
      manufacturingInstructions: hierarchicalFormData.manufacturingInstructions,
      estimatedBuildTime: hierarchicalFormData.estimatedBuildTime,
      partsRequired: mergedPartsRequired // Use merged data instead of direct overwrite
    };

    // Basic validation before saving
    if (!freshDataToSave.name?.trim()) {
      toast.error('Assembly name is required');
      return;
    }

    if (!freshDataToSave.assemblyCode?.trim()) {
      toast.error('Assembly Code is required');
      return;
    }

    // Validate that assembly has at least one part
    if (!freshDataToSave.partsRequired || !Array.isArray(freshDataToSave.partsRequired) || freshDataToSave.partsRequired.length === 0) {
      toast.error('Assembly must have at least one part');
      return;
    }

    // Check for invalid part references
    const invalidParts = freshDataToSave.partsRequired.filter((part: any) => {
      const partRef = part.partId || part.item_id;
      return !partRef || (typeof partRef === 'object' && !partRef._id);
    });

    if (invalidParts.length > 0) {
      toast.error(`${invalidParts.length} part(s) have invalid or missing references`);
      return;
    }

    // Save using the fresh data directly
    const success = await saveAssemblyWithFreshData(freshDataToSave);
    if (success) {
      toast.success(`Assembly ${isEditing ? 'updated' : 'created'} successfully`);
      router.push('/assemblies');
    }
  }, [formData, saveAssemblyWithFreshData, isEditing, router, validatePartsData]); // Updated dependencies

  // Handle cancel button click
  const handleCancel = () => {
    if (isDirty) {
      if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        resetForm();
        router.push('/assemblies');
      }
    } else {
      router.push('/assemblies');
    }
  };

  if (isLoading) {
    return (
      <div className="container py-8 space-y-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-yellow-500" />
          <span className="ml-2 text-lg">Loading assembly...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 max-w-5xl">
      <div className="flex justify-between items-center mb-6">
        <Button variant="ghost" onClick={handleCancel} className="text-muted-foreground hover:text-foreground">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Assemblies
        </Button>
        <div className="flex gap-2 items-center">
          {/* Stock staleness indicator */}
          {stockStaleness.hasStaleData && (
            <div className="flex items-center gap-1 text-amber-600 text-sm">
              <AlertTriangle className="h-4 w-4" />
              <span>{stockStaleness.staleParts.length} parts may have stale stock data</span>
            </div>
          )}

          {!stockStaleness.hasStaleData && formData?.partsRequired?.length > 0 && (
            <div className="flex items-center gap-1 text-green-600 text-sm">
              <CheckCircle className="h-4 w-4" />
              <span>Stock data is current</span>
            </div>
          )}

          <Button
            variant="outline"
            onClick={handleStockRefresh}
            disabled={isLoading || isSaving}
            className={`${
              refreshRecommendation.priority === 'high'
                ? 'text-red-600 border-red-600 hover:bg-red-50'
                : refreshRecommendation.priority === 'medium'
                ? 'text-amber-600 border-amber-600 hover:bg-amber-50'
                : 'text-blue-600 border-blue-600 hover:bg-blue-50'
            }`}
            title={
              refreshRecommendation.shouldRefresh
                ? `Stock refresh recommended: ${refreshRecommendation.reasons.join(', ')}`
                : 'Refresh stock data for all parts'
            }
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Stock
            {refreshRecommendation.shouldRefresh && (
              <span className="ml-1 text-xs">
                ({refreshRecommendation.priority})
              </span>
            )}
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Assembly
              </>
            )}
          </Button>
        </div>
      </div>

      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle>Assembly Details</CardTitle>
        </CardHeader>
        <CardContent>
          <HierarchicalPartsForm
            ref={hierarchicalPartsFormRef}
            initialData={formattedAssemblyData}
            mode={isEditing ? "edit" : "create"}
            onFormSubmit={handleFormSubmit}
          />
        </CardContent>
      </Card>
    </div>
  );
}
